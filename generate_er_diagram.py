#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学生数据库E-R图生成器
基于students.sql文件生成实体关系图
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_er_diagram():
    """创建E-R图"""
    fig, ax = plt.subplots(1, 1, figsize=(14, 10))
    ax.set_xlim(0, 14)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # 定义颜色
    entity_color = '#E8F4FD'
    attribute_color = '#FFF2CC'
    key_color = '#FFE6CC'
    relationship_color = '#E1D5E7'
    
    # 绘制Students实体
    students_box = FancyBboxPatch(
        (1, 6), 4, 3,
        boxstyle="round,pad=0.1",
        facecolor=entity_color,
        edgecolor='black',
        linewidth=2
    )
    ax.add_patch(students_box)
    ax.text(3, 7.5, 'Students\n(学生)', ha='center', va='center', fontsize=14, fontweight='bold')
    
    # 绘制Classes实体
    classes_box = FancyBboxPatch(
        (9, 6), 4, 3,
        boxstyle="round,pad=0.1",
        facecolor=entity_color,
        edgecolor='black',
        linewidth=2
    )
    ax.add_patch(classes_box)
    ax.text(11, 7.5, 'Classes\n(班级)', ha='center', va='center', fontsize=14, fontweight='bold')
    
    # 绘制关系
    relationship_diamond = FancyBboxPatch(
        (6, 6.5), 2, 2,
        boxstyle="round,pad=0.1",
        facecolor=relationship_color,
        edgecolor='black',
        linewidth=2
    )
    ax.add_patch(relationship_diamond)
    ax.text(7, 7.5, '属于', ha='center', va='center', fontsize=12, fontweight='bold')
    
    # 绘制连接线
    # Students到关系
    ax.plot([5, 6], [7.5, 7.5], 'k-', linewidth=2)
    # 关系到Classes
    ax.plot([8, 9], [7.5, 7.5], 'k-', linewidth=2)
    
    # 添加基数标注
    ax.text(5.5, 8, 'N', ha='center', va='center', fontsize=12, fontweight='bold')
    ax.text(8.5, 8, '1', ha='center', va='center', fontsize=12, fontweight='bold')
    
    # Students属性
    student_attributes = [
        ('student_id', '学生ID', True, 1, 4.5),
        ('student_no', '学号', False, 1, 3.8),
        ('student_name', '姓名', False, 1, 3.1),
        ('phone', '电话', False, 1, 2.4),
        ('email', '邮箱', False, 1, 1.7),
        ('create_time', '创建时间', False, 1, 1.0)
    ]
    
    for attr_name, attr_chinese, is_key, x, y in student_attributes:
        color = key_color if is_key else attribute_color
        attr_box = FancyBboxPatch(
            (x-0.4, y-0.2), 2.8, 0.4,
            boxstyle="round,pad=0.05",
            facecolor=color,
            edgecolor='black',
            linewidth=1
        )
        ax.add_patch(attr_box)
        
        text = f"{attr_name}\n({attr_chinese})"
        if is_key:
            text = f"🔑 {text}"
        ax.text(x+1, y, text, ha='center', va='center', fontsize=9)
        
        # 连接线到实体
        ax.plot([x****, 3], [y, 6], 'k-', linewidth=1, alpha=0.7)
    
    # Classes属性（推断的）
    class_attributes = [
        ('class_id', '班级ID', True, 11, 4.5),
        ('class_name', '班级名称', False, 11, 3.8),
        ('class_teacher', '班主任', False, 11, 3.1),
        ('grade', '年级', False, 11, 2.4),
        ('create_time', '创建时间', False, 11, 1.7)
    ]
    
    for attr_name, attr_chinese, is_key, x, y in class_attributes:
        color = key_color if is_key else attribute_color
        attr_box = FancyBboxPatch(
            (x-0.4, y-0.2), 2.8, 0.4,
            boxstyle="round,pad=0.05",
            facecolor=color,
            edgecolor='black',
            linewidth=1
        )
        ax.add_patch(attr_box)
        
        text = f"{attr_name}\n({attr_chinese})"
        if is_key:
            text = f"🔑 {text}"
        ax.text(x+1, y, text, ha='center', va='center', fontsize=9)
        
        # 连接线到实体
        ax.plot([x+0.6, 11], [y, 6], 'k-', linewidth=1, alpha=0.7)
    
    # 添加标题
    ax.text(7, 9.5, '学生管理系统 E-R 图', ha='center', va='center', 
            fontsize=18, fontweight='bold')
    
    # 添加图例
    legend_y = 0.5
    ax.text(0.5, legend_y, '图例:', fontsize=12, fontweight='bold')
    
    # 实体图例
    entity_legend = FancyBboxPatch(
        (1.5, legend_y-0.15), 1, 0.3,
        boxstyle="round,pad=0.05",
        facecolor=entity_color,
        edgecolor='black'
    )
    ax.add_patch(entity_legend)
    ax.text(2, legend_y, '实体', ha='center', va='center', fontsize=10)
    
    # 关系图例
    rel_legend = FancyBboxPatch(
        (3, legend_y-0.15), 1, 0.3,
        boxstyle="round,pad=0.05",
        facecolor=relationship_color,
        edgecolor='black'
    )
    ax.add_patch(rel_legend)
    ax.text(3.5, legend_y, '关系', ha='center', va='center', fontsize=10)
    
    # 主键图例
    key_legend = FancyBboxPatch(
        (4.5, legend_y-0.15), 1, 0.3,
        boxstyle="round,pad=0.05",
        facecolor=key_color,
        edgecolor='black'
    )
    ax.add_patch(key_legend)
    ax.text(5, legend_y, '主键', ha='center', va='center', fontsize=10)
    
    # 属性图例
    attr_legend = FancyBboxPatch(
        (6, legend_y-0.15), 1, 0.3,
        boxstyle="round,pad=0.05",
        facecolor=attribute_color,
        edgecolor='black'
    )
    ax.add_patch(attr_legend)
    ax.text(6.5, legend_y, '属性', ha='center', va='center', fontsize=10)
    
    plt.tight_layout()
    return fig

def create_detailed_table_diagram():
    """创建详细的表结构图"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 12)
    ax.axis('off')
    
    # Students表
    students_data = [
        ['字段名', '数据类型', '约束', '说明'],
        ['student_id', 'bigint(20)', 'PK, AUTO_INCREMENT', '学生唯一标识'],
        ['student_no', 'varchar(20)', 'UNIQUE, NOT NULL', '学生学号'],
        ['student_name', 'varchar(30)', 'NOT NULL', '学生姓名'],
        ['class_id', 'bigint(20)', 'FK, NULL', '所属班级ID'],
        ['phone', 'varchar(11)', 'NULL', '联系电话'],
        ['email', 'varchar(50)', 'NULL', '电子邮箱'],
        ['create_by', 'varchar(64)', 'NULL', '创建者'],
        ['create_time', 'datetime', 'NULL', '创建时间'],
        ['update_by', 'varchar(64)', 'NULL', '更新者'],
        ['update_time', 'datetime', 'NULL', '更新时间']
    ]
    
    # 绘制Students表
    draw_table(ax, 1, 8, 6, 3, 'Students (学生表)', students_data)
    
    # Classes表（推断结构）
    classes_data = [
        ['字段名', '数据类型', '约束', '说明'],
        ['class_id', 'bigint(20)', 'PK, AUTO_INCREMENT', '班级唯一标识'],
        ['class_name', 'varchar(50)', 'NOT NULL', '班级名称'],
        ['class_teacher', 'varchar(30)', 'NULL', '班主任'],
        ['grade', 'varchar(10)', 'NULL', '年级'],
        ['create_time', 'datetime', 'NULL', '创建时间']
    ]
    
    # 绘制Classes表
    draw_table(ax, 9, 8, 6, 3, 'Classes (班级表)', classes_data)
    
    # 绘制关系线
    ax.annotate('', xy=(9, 9.5), xytext=(7, 9.5),
                arrowprops=dict(arrowstyle='->', lw=2, color='red'))
    ax.text(8, 10, 'class_id (FK)', ha='center', va='center', 
            fontsize=10, color='red', fontweight='bold')
    
    # 添加标题
    ax.text(8, 11.5, '学生管理系统 - 数据库表结构图', ha='center', va='center', 
            fontsize=18, fontweight='bold')
    
    plt.tight_layout()
    return fig

def draw_table(ax, x, y, width, height, title, data):
    """绘制表格"""
    # 表格边框
    table_box = FancyBboxPatch(
        (x, y-height), width, height,
        boxstyle="round,pad=0.1",
        facecolor='white',
        edgecolor='black',
        linewidth=2
    )
    ax.add_patch(table_box)
    
    # 表格标题
    title_box = FancyBboxPatch(
        (x, y-0.5), width, 0.5,
        boxstyle="round,pad=0.05",
        facecolor='#4472C4',
        edgecolor='black',
        linewidth=1
    )
    ax.add_patch(title_box)
    ax.text(x + width/2, y-0.25, title, ha='center', va='center', 
            fontsize=12, fontweight='bold', color='white')
    
    # 表格内容
    row_height = (height - 0.5) / len(data)
    for i, row in enumerate(data):
        row_y = y - 0.5 - (i + 1) * row_height
        
        # 表头行背景
        if i == 0:
            header_box = FancyBboxPatch(
                (x, row_y), width, row_height,
                boxstyle="square,pad=0",
                facecolor='#D9E2F3',
                edgecolor='black',
                linewidth=0.5
            )
            ax.add_patch(header_box)
        
        # 绘制行内容
        col_width = width / len(row)
        for j, cell in enumerate(row):
            cell_x = x + j * col_width
            ax.text(cell_x + col_width/2, row_y + row_height/2, str(cell), 
                   ha='center', va='center', fontsize=8, 
                   fontweight='bold' if i == 0 else 'normal')
            
            # 绘制列分隔线
            if j < len(row) - 1:
                ax.plot([cell_x + col_width, cell_x + col_width], 
                       [row_y, row_y + row_height], 'k-', linewidth=0.5)
        
        # 绘制行分隔线
        if i < len(data) - 1:
            ax.plot([x, x + width], [row_y, row_y], 'k-', linewidth=0.5)

if __name__ == "__main__":
    print("正在生成学生数据库E-R图...")
    
    # 生成E-R图
    fig1 = create_er_diagram()
    fig1.savefig('students_er_diagram.png', dpi=300, bbox_inches='tight')
    print("E-R图已保存为: students_er_diagram.png")
    
    # 生成详细表结构图
    fig2 = create_detailed_table_diagram()
    fig2.savefig('students_table_structure.png', dpi=300, bbox_inches='tight')
    print("表结构图已保存为: students_table_structure.png")
    
    plt.show()
    print("图表生成完成！")
